use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::dto::{ApiResponse, CreateNoteDraftRequest, CreateNoteDraftResponse, ErrorCode};
use crate::services::mysql::MySqlNoteDraftServiceError;
use crate::middleware::token_parser::get_user_id_from_request;
use crate::AppState;

/**
 * 创建笔记草稿处理函数
 *
 * 此函数处理POST /note/draft/create请求，用于创建笔记草稿
 *
 * @param req HTTP请求
 * @param payload 请求体，包含笔记草稿创建信息
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<CreateNoteDraftRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析笔记ID
    let note_id = match payload.note_id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("笔记ID格式错误: {}", payload.note_id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "笔记ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取MySQL笔记服务，验证笔记是否存在且属于当前用户
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证笔记是否存在且属于当前用户
    match mysql_note_service.get_note_by_id(note_id).await {
        Ok(note) => {
            if note.user_id != user_id {
                error!("用户 {} 无权限访问笔记 {}", user_id, note_id);
                return HttpResponse::Forbidden().json(ApiResponse {
                    code: ErrorCode::InvalidParameter.code(),
                    message: "无权限访问该笔记".to_string(),
                    data: Value::Null,
                });
            }
        }
        Err(_) => {
            error!("笔记不存在: {}", note_id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "笔记不存在".to_string(),
                data: Value::Null,
            });
        }
    }

    // 获取MySQL笔记草稿服务
    let mysql_note_draft_service = match &app_state.mysql_note_draft_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记草稿服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建或更新笔记草稿
    match mysql_note_draft_service.create_or_update_draft(
        note_id,
        user_id,
        payload.content.clone(),
    ).await {
        Ok(draft) => {
            let draft_id_str = draft.id.to_string();
            info!("笔记草稿创建或更新成功，草稿ID: {}", draft_id_str);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "笔记草稿创建成功".to_string(),
                data: CreateNoteDraftResponse {
                    id: draft_id_str,
                },
            })
        }
        Err(e) => {
            error!("创建或更新笔记草稿失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteDraftServiceError::UserHasOtherDraft => {
                    (ErrorCode::InvalidParameter, "您已有其他草稿，请先完成或删除现有草稿")
                }
                MySqlNoteDraftServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "服务错误，请稍后再试")
                }
                MySqlNoteDraftServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteDraftServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
                MySqlNoteDraftServiceError::DraftNotFound => {
                    (ErrorCode::InvalidParameter, "草稿不存在")
                }
                _ => {
                    (ErrorCode::DatabaseError, "服务错误，请稍后再试")
                }
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: Value::Null,
            })
        }
    }
}
