use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, UpdateNoteRequest, UpdateNoteResponse},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlNoteServiceError,
};

/**
 * 更新笔记处理函数
 *
 * 此函数处理POST /note/update请求，用于更新笔记的内容
 *
 * @param req HTTP请求
 * @param payload 请求体，包含笔记更新信息
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<UpdateNoteRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let _user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析笔记ID
    let note_id = match payload.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("笔记ID格式错误: {}", payload.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "笔记ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证至少有一个字段需要更新
    if payload.title.is_none() 
        && payload.cover.is_none() 
        && payload.desc.is_none() 
        && payload.content.is_none() 
        && payload.html.is_none() {
        error!("没有提供任何需要更新的字段");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "至少需要提供一个要更新的字段".to_string(),
            data: Value::Null,
        });
    }

    // 获取MySQL笔记服务
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 更新笔记
    match mysql_note_service.update_note(
        note_id,
        payload.title.clone(),
        payload.cover.clone(),
        payload.desc.clone(),
        payload.content.clone(),
        payload.html.clone(),
    ).await {
        Ok(note) => {
            let note_id_str = note.id.to_string();
            info!("笔记更新成功，笔记ID: {}", note_id_str);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "笔记更新成功".to_string(),
                data: UpdateNoteResponse {
                    id: note_id_str,
                },
            })
        }
        Err(e) => {
            error!("更新笔记失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteServiceError::NoteNotFound => {
                    (ErrorCode::InvalidParameter, "笔记不存在")
                }
                MySqlNoteServiceError::PermissionDenied => {
                    (ErrorCode::InvalidParameter, "无权限访问该笔记")
                }
                MySqlNoteServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "服务错误，请稍后再试")
                }
                MySqlNoteServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: Value::Null,
            })
        }
    }
}
