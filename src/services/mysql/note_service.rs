use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::{
    config::AppConfig,
    db::utils::mysql_helper,
    db::DbConnections,
    models::mysql::MySqlNote,
};

/// MySQL笔记服务错误类型
#[derive(Debug, Error)]
pub enum MySqlNoteServiceError {
    /// 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    /// 笔记不存在
    #[error("笔记不存在")]
    NoteNotFound,

    /// 权限不足
    #[error("权限不足")]
    PermissionDenied,

    /// 数据库连接不可用
    #[error("数据库连接不可用")]
    DatabaseNotAvailable,

    /// 参数无效
    #[error("参数无效")]
    InvalidParameter,
}

/// MySQL笔记服务，处理笔记相关的业务逻辑
pub struct MySqlNoteService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlNoteService {
    /**
     * 创建新的MySQL笔记服务实例
     *
     * @param db_connections 数据库连接管理器
     * @param config 应用配置
     * @return 返回 MySqlNoteService 实例或错误
     */
    pub fn new(db_connections: &DbConnections, _config: &Arc<AppConfig>) -> Result<Self, MySqlNoteServiceError> {
        // 获取MySQL连接池
        let pool = mysql_helper::get_mysql_pool(db_connections)
            .ok_or(MySqlNoteServiceError::DatabaseNotAvailable)?;

        Ok(Self {
            pool: Arc::new(pool.clone()),
        })
    }

    /**
     * 根据ID获取笔记详情（不验证用户权限）
     *
     * @param note_id 笔记ID
     * @return 成功返回笔记详情，失败返回错误
     */
    pub async fn get_note_by_id(&self, note_id: u64) -> Result<MySqlNote, MySqlNoteServiceError> {
        // 查询笔记
        let note = sqlx::query_as::<_, MySqlNote>(
            "SELECT * FROM notes WHERE id = ?"
        )
        .bind(note_id)
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlNoteServiceError::NoteNotFound)?;

        Ok(note)
    }

    /**
     * 创建新笔记
     *
     * @param parent_id 合集ID（可选）
     * @param user_id 用户ID
     * @param title 笔记标题
     * @param cover 笔记封面
     * @param desc 笔记描述
     * @param content 笔记内容（可选）
     * @param html HTML内容
     * @return 成功返回新创建的笔记，失败返回错误
     */
    pub async fn create_note(
        &self,
        parent_id: Option<u64>,
        user_id: u64,
        title: String,
        cover: String,
        desc: String,
        content: Option<String>,
        html: String,
    ) -> Result<MySqlNote, MySqlNoteServiceError> {
        // 验证参数
        if title.trim().is_empty() {
            return Err(MySqlNoteServiceError::InvalidParameter);
        }

        // 如果指定了合集ID，验证合集是否存在且属于该用户
        if let Some(parent_id_val) = parent_id {
            // 这里可以添加验证合集是否存在的逻辑
            // 暂时跳过，因为需要合集服务
            let _ = parent_id_val;
        }

        // 插入笔记
        let note_id = sqlx::query(
            "INSERT INTO notes (parent_id, user_id, title, cover, `desc`, content, html) VALUES (?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(parent_id)
        .bind(user_id)
        .bind(&title)
        .bind(&cover)
        .bind(&desc)
        .bind(&content)
        .bind(&html)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的笔记
        let note = sqlx::query_as::<_, MySqlNote>(
            "SELECT * FROM notes WHERE id = ?"
        )
        .bind(note_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(note)
    }

    /**
     * 根据ID获取笔记详情（不验证用户权限，仅用于内部调用）
     *
     * @param note_id 笔记ID
     * @return 成功返回笔记详情选项，失败返回错误
     */
    pub async fn get_note_by_id_internal(&self, note_id: u64) -> Result<Option<MySqlNote>, MySqlNoteServiceError> {
        let note = sqlx::query_as::<_, MySqlNote>(
            "SELECT * FROM notes WHERE id = ?"
        )
        .bind(note_id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(note)
    }

    /**
     * 更新笔记
     *
     * @param note_id 笔记ID
     * @param title 新标题（可选）
     * @param cover 新封面（可选）
     * @param desc 新描述（可选）
     * @param content 新内容（可选）
     * @param html 新HTML内容（可选）
     * @return 成功返回更新后的笔记，失败返回错误
     */
    pub async fn update_note(
        &self,
        note_id: u64,
        title: Option<String>,
        cover: Option<String>,
        desc: Option<String>,
        content: Option<String>,
        html: Option<String>,
    ) -> Result<MySqlNote, MySqlNoteServiceError> {
        // 检查笔记是否存在
        let _note = self.get_note_by_id(note_id).await?;

        // 构建更新SQL
        let mut sql = String::from("UPDATE notes SET ");
        let mut params = Vec::new();
        let mut has_updates = false;

        // 添加标题更新
        if let Some(title_value) = title {
            sql.push_str("title = ?");
            params.push(title_value);
            has_updates = true;
        }

        // 添加封面更新
        if let Some(cover_value) = cover {
            if has_updates {
                sql.push_str(", ");
            }
            sql.push_str("cover = ?");
            params.push(cover_value);
            has_updates = true;
        }

        // 添加描述更新
        if let Some(desc_value) = desc {
            if has_updates {
                sql.push_str(", ");
            }
            sql.push_str("`desc` = ?");
            params.push(desc_value);
            has_updates = true;
        }

        // 添加内容更新
        if let Some(content_value) = content {
            if has_updates {
                sql.push_str(", ");
            }
            sql.push_str("content = ?");
            params.push(content_value);
            has_updates = true;
        }

        // 添加HTML内容更新
        if let Some(html_value) = html {
            if has_updates {
                sql.push_str(", ");
            }
            sql.push_str("html = ?");
            params.push(html_value);
            has_updates = true;
        }

        // 如果没有任何更新，直接返回原笔记
        if !has_updates {
            return self.get_note_by_id(note_id).await;
        }

        // 添加更新时间
        sql.push_str(", update_time = CURRENT_TIMESTAMP WHERE id = ?");

        // 执行更新
        let mut query = sqlx::query(&sql);
        for param in params {
            query = query.bind(param);
        }
        query = query.bind(note_id);

        query.execute(&*self.pool).await?;

        // 获取更新后的笔记
        let updated_note = sqlx::query_as::<_, MySqlNote>(
            "SELECT * FROM notes WHERE id = ?"
        )
        .bind(note_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(updated_note)
    }

    /**
     * 获取用户的笔记列表（带分页）
     *
     * @param user_id 用户ID
     * @param parent_id 合集ID（可选，如果指定则只获取该合集下的笔记）
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回笔记列表和总数，失败返回错误
     */
    pub async fn get_notes_by_user_id(
        &self,
        user_id: u64,
        parent_id: Option<u64>,
        page: u32,
        page_size: u32,
    ) -> Result<(Vec<MySqlNote>, u64), MySqlNoteServiceError> {
        // 计算偏移量
        let offset = (page - 1) * page_size;

        // 根据是否有parent_id构建不同的查询
        let (count_sql, data_sql) = if parent_id.is_some() {
            (
                "SELECT COUNT(*) FROM notes WHERE user_id = ? AND parent_id = ?",
                "SELECT * FROM notes WHERE user_id = ? AND parent_id = ? ORDER BY create_time DESC LIMIT ? OFFSET ?"
            )
        } else {
            (
                "SELECT COUNT(*) FROM notes WHERE user_id = ?",
                "SELECT * FROM notes WHERE user_id = ? ORDER BY create_time DESC LIMIT ? OFFSET ?"
            )
        };

        // 获取总数
        let total: (i64,) = if let Some(parent_id_val) = parent_id {
            sqlx::query_as(count_sql)
                .bind(user_id)
                .bind(parent_id_val)
                .fetch_one(&*self.pool)
                .await?
        } else {
            sqlx::query_as(count_sql)
                .bind(user_id)
                .fetch_one(&*self.pool)
                .await?
        };

        // 获取笔记列表
        let notes = if let Some(parent_id_val) = parent_id {
            sqlx::query_as::<_, MySqlNote>(data_sql)
                .bind(user_id)
                .bind(parent_id_val)
                .bind(page_size)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
        } else {
            sqlx::query_as::<_, MySqlNote>(data_sql)
                .bind(user_id)
                .bind(page_size)
                .bind(offset)
                .fetch_all(&*self.pool)
                .await?
        };

        Ok((notes, total.0 as u64))
    }
}
